"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useUser } from "@stackframe/stack";
import { Search, LayoutDashboard, Calendar, ChevronRight, PanelLeftOpen, PanelLeftClose, ChevronDown, Settings, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { UserMenu } from "@/components/user-menu";
import { useListColor } from "@/contexts/list-color-context";
import { renderSpaceIcon } from "@/lib/space-icons";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

// Custom N Icon component for Tasks
const NIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <Image
    src="/NeoTask_Icon_N.webp"
    alt="Tasks"
    width={24}
    height={24}
    className={`${className} object-contain`}
    style={style}
  />
);

const routes = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
  },
  {
    href: "/tasks",
    label: "Tasks",
    icon: NIcon,
  },
  {
    href: "/calendar",
    label: "Calendar",
    icon: Calendar,
  },
];

interface SidebarProps {
  currentSpace?: { id: string; name: string; icon?: string | null } | null;
  onSpaceClick?: () => void;
  isSpaceLoading?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

// Desktop User Section Component
function DesktopUserSection({ isCollapsed }: { isCollapsed?: boolean }) {
  const user = useUser();
  const router = useRouter();

  const handleSignOut = async () => {
    if (user) {
      await user.signOut();
      router.push("/handler/sign-in");
    }
  };

  if (!user) return null;

  if (isCollapsed) {
    // Collapsed state: Just avatar with dropdown
    return (
      <div className="flex justify-center px-3 py-2">
        <DropdownMenu>
          <DropdownMenuTrigger className="focus:outline-none">
            <Avatar className="h-5 w-5 cursor-pointer">
              <AvatarImage src={user.avatarUrl || ""} alt={user.displayName || "User"} />
              <AvatarFallback className="text-xs">
                {user.displayName?.charAt(0) || user.primaryEmail?.charAt(0) || "U"}
              </AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center" className="w-48">
            <DropdownMenuItem onClick={handleSignOut} className="text-destructive focus:text-destructive">
              <LogOut className="mr-2 h-4 w-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  // Expanded state: Avatar with user info inline and dropdown for sign out only
  return (
    <div className="flex items-center gap-3 px-3 py-2">
      <DropdownMenu>
        <DropdownMenuTrigger className="focus:outline-none">
          <Avatar className="h-5 w-5 cursor-pointer flex-shrink-0">
            <AvatarImage src={user.avatarUrl || ""} alt={user.displayName || "User"} />
            <AvatarFallback className="text-xs">
              {user.displayName?.charAt(0) || user.primaryEmail?.charAt(0) || "U"}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          <DropdownMenuItem onClick={handleSignOut} className="text-destructive focus:text-destructive">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Sign out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <div className="flex flex-col min-w-0 flex-1">
        <p className="text-sm font-medium truncate">{user.displayName || "User"}</p>
        <p className="text-xs text-muted-foreground truncate">{user.primaryEmail}</p>
      </div>
    </div>
  );
}

export function Sidebar({
  currentSpace = null,
  onSpaceClick,
  isSpaceLoading = false,
  isCollapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  const [currentPath, setCurrentPath] = useState("");
  const [isHovered, setIsHovered] = useState(false);
  const pathname = usePathname();
  const { currentListColor } = useListColor();

  useEffect(() => {
    // Only set the pathname on the client side
    setCurrentPath(pathname || "");
  }, [pathname]);

  // Get logo styles based on current list color
  const getLogoStyles = () => {
    if (!currentListColor) {
      // Plain white for colorless lists with subtle glow
      return {
        backgroundColor: '#ffffff',
        boxShadow: '0 0 8px rgba(255, 255, 255, 0.3), 0 0 16px rgba(255, 255, 255, 0.1)',
      };
    }

    // Apply solid color background based on current list color with subtle glow
    return {
      backgroundColor: currentListColor,
      boxShadow: `0 0 8px ${currentListColor}40, 0 0 16px ${currentListColor}20`,
    };
  };

  // Get grey filter for inactive N icon to match other inactive tab icons
  const getInactiveNIconFilter = () => {
    // Convert white N icon to muted grey color similar to other inactive icons
    return "brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%)";
  };

  return (
    <aside
      className={cn(
        "fixed left-0 top-0 z-40 h-full bg-background border-r border-border/50 transition-all duration-300 ease-in-out",
        isCollapsed ? "w-16" : "w-64"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex flex-col h-full">
        {/* Header with logo and toggle button */}
        <div className={cn(
          "flex items-center transition-all duration-300",
          isCollapsed ? "justify-center p-4" : "py-4"
        )}>
          {isCollapsed ? (
            // Collapsed state: N icon with hover toggle button
            <div className="relative flex items-center">
              {/* N Icon */}
              <div
                className={cn(
                  "w-10 h-10 transition-all duration-300",
                  isHovered ? "opacity-0 scale-95" : "opacity-100 scale-100"
                )}
                style={{
                  ...getLogoStyles(),
                  WebkitMask: 'url(/NeoTask_Icon_N.webp) no-repeat center/contain',
                  mask: 'url(/NeoTask_Icon_N.webp) no-repeat center/contain',
                }}
              />

              {/* Toggle Button - appears on hover */}
              <button
                type="button"
                onClick={onToggleCollapse}
                className={cn(
                  "absolute inset-0 flex items-center justify-center w-10 h-10 rounded-lg bg-muted/80 hover:bg-muted transition-all duration-300 cursor-pointer border border-border/20",
                  isHovered ? "opacity-100 scale-100" : "opacity-0 scale-95 pointer-events-none"
                )}
                title="Expand sidebar"
              >
                <PanelLeftOpen className="h-4 w-4 text-foreground" />
              </button>
            </div>
          ) : (
            // Expanded state: Left-aligned logo with right-aligned collapse button
            <div className="flex items-center w-full">
              <div
                className="w-32 h-12 ml-7"
                style={{
                  ...getLogoStyles(),
                  WebkitMask: 'url(/NeoTask_Logo_white.webp) no-repeat left center/contain',
                  mask: 'url(/NeoTask_Logo_white.webp) no-repeat left center/contain',
                }}
              />

              {/* Spacer to push collapse button to the right */}
              <div className="flex-1" />

              {/* Collapse Button - right aligned */}
              <button
                type="button"
                onClick={onToggleCollapse}
                className="flex items-center justify-center w-8 h-8 rounded-lg bg-muted/50 hover:bg-muted transition-all duration-200 cursor-pointer mr-4"
                title="Collapse sidebar"
              >
                <PanelLeftClose className="h-4 w-4 text-muted-foreground" />
              </button>
            </div>
          )}
        </div>

        {/* Space Section */}
        {currentSpace && (
          <div className="px-4 py-1">
            <Button
              variant="ghost"
              onClick={onSpaceClick}
              className={cn(
                "w-full justify-start gap-3 space-name-glass rounded-lg px-3 py-2 h-auto cursor-pointer",
                isCollapsed && "justify-center px-2 py-2"
              )}
              disabled={isSpaceLoading}
              title={isCollapsed ? currentSpace.name : undefined}
            >
              <div className="flex-shrink-0">
                {renderSpaceIcon(currentSpace.icon, "h-5 w-5")}
              </div>
              {!isCollapsed && (
                <span className="text-sm font-medium truncate flex-1 text-left">
                  {currentSpace.name}
                </span>
              )}
              {!isCollapsed && !isSpaceLoading && (
                <ChevronDown className="h-3 w-3 text-muted-foreground flex-shrink-0" />
              )}
              {isSpaceLoading && !isCollapsed && (
                <LoadingSpinner size="sm" />
              )}
            </Button>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {routes.map((route) => {
            const Icon = route.icon;
            const isActive = currentPath === route.href;
            const isTasksTab = route.href === "/tasks";

            return (
              <Link
                key={route.href}
                href={route.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-colors hover:bg-muted/50",
                  isActive ? "bg-muted text-foreground" : "text-muted-foreground hover:text-foreground",
                  isCollapsed && "justify-center px-2"
                )}
                title={isCollapsed ? route.label : undefined}
              >
                {isTasksTab ? (
                  <Icon
                    className="h-5 w-5 flex-shrink-0"
                    style={!isActive ? {
                      filter: getInactiveNIconFilter()
                    } : undefined}
                  />
                ) : (
                  <Icon className="h-5 w-5 flex-shrink-0" />
                )}
                {!isCollapsed && <span className="font-medium">{route.label}</span>}
              </Link>
            );
          })}
        </nav>

        {/* Search Section */}
        <div className="px-4 py-2">
          {!isCollapsed ? (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                className="pl-9"
              />
            </div>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              className="w-full h-10"
              title="Search"
            >
              <Search className="h-4 w-4" />
              <span className="sr-only">Search</span>
            </Button>
          )}
        </div>



        {/* Settings and User Section */}
        <div className="p-4 space-y-2">
          <Link
            href="/settings"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-lg transition-colors hover:bg-muted/50 text-muted-foreground hover:text-foreground",
              isCollapsed && "justify-center px-2"
            )}
            title={isCollapsed ? "Settings" : undefined}
          >
            <Settings className="h-5 w-5 flex-shrink-0" />
            {!isCollapsed && <span className="font-medium">Settings</span>}
          </Link>

          <DesktopUserSection isCollapsed={isCollapsed} />
        </div>
      </div>
    </aside>
  );
}
